const axios = require('axios');
const tykConfig = require('./tyk');
const config = require('./config');
const logger = require('./logger');
const { Identity } = require('../models');

/**
 * Tyk SSO Integration
 * Handles OAuth, SAML, and OIDC authentication through Tyk Gateway
 * Replaces passport strategies when Tyk mode is enabled
 */

const tykSSO = {
  isEnabled: config.auth.mode === 'tyk' && config.tyk.enabled,
  gatewayUrl: tykConfig.gatewayUrl,
  callbackBaseUrl: config.server_url,

  /**
   * Initialize SAML authentication with Tyk
   */
  async initializeSAML() {
    if (!this.isEnabled) return null;

    try {
      const samlConfig = {
        name: "SAML Authentication",
        api_id: "saml-auth-api",
        org_id: tykConfig.orgId,
        use_keyless: false,
        use_oauth2: false,
        use_openid: false,
        enable_jwt: false,
        use_basic_auth: false,
        use_mutual_tls: false,
        custom_middleware: {
          pre: [],
          post: [],
          post_key_auth: [],
          auth_check: {
            name: "SAMLAuthMiddleware",
            path: "/tyk/middleware/saml",
            require_session: false,
            raw_body_only: false
          },
          response: [],
          driver: "grpc",
          id_extractor: {
            extract_from: "header",
            extract_with: "x-saml-user-id",
            extractor_config: {}
          }
        },
        proxy: {
          listen_path: "/auth/saml/",
          target_url: `${this.callbackBaseUrl}/auth/saml/callback`,
          strip_listen_path: true
        },
        active: true
      };

      const response = await axios.post(
        `${this.gatewayUrl}/tyk/apis`,
        samlConfig,
        {
          headers: tykConfig.getGatewayHeaders(),
          timeout: 10000
        }
      );

      logger.info('Tyk SAML authentication initialized');
      return response.data;
    } catch (error) {
      logger.error('Failed to initialize Tyk SAML:', error.message);
      throw error;
    }
  },

  /**
   * Initialize OAuth 2.0 authentication with Tyk
   */
  async initializeOAuth() {
    if (!this.isEnabled) return null;

    try {
      const oauthConfig = {
        name: "OAuth 2.0 Authentication",
        api_id: "oauth-auth-api",
        org_id: tykConfig.orgId,
        use_keyless: false,
        use_oauth2: true,
        oauth_meta: {
          allowed_access_types: ["authorization_code", "refresh_token"],
          allowed_authorize_types: ["code", "token"],
          auth_login_redirect: "/auth/oauth/login"
        },
        enable_jwt: false,
        use_basic_auth: false,
        use_mutual_tls: false,
        proxy: {
          listen_path: "/auth/oauth/",
          target_url: `${this.callbackBaseUrl}/auth/oauth/callback`,
          strip_listen_path: true
        },
        active: true
      };

      const response = await axios.post(
        `${this.gatewayUrl}/tyk/apis`,
        oauthConfig,
        {
          headers: tykConfig.getGatewayHeaders(),
          timeout: 10000
        }
      );

      logger.info('Tyk OAuth 2.0 authentication initialized');
      return response.data;
    } catch (error) {
      logger.error('Failed to initialize Tyk OAuth:', error.message);
      throw error;
    }
  },

  /**
   * Initialize OpenID Connect authentication with Tyk
   */
  async initializeOIDC() {
    if (!this.isEnabled) return null;

    try {
      const oidcConfig = {
        name: "OpenID Connect Authentication",
        api_id: "oidc-auth-api",
        org_id: tykConfig.orgId,
        use_keyless: false,
        use_oauth2: false,
        use_openid: true,
        openid_options: {
          providers: [
            {
              issuer: config.oidc.issuer,
              client_ids: {
                [config.oidc.clientID]: config.oidc.clientSecret
              }
            }
          ],
          segregate_by_client: false
        },
        enable_jwt: true,
        jwt_signing_method: "RS256",
        jwt_source: config.oidc.issuer + "/.well-known/jwks.json",
        jwt_identity_base_field: "sub",
        use_basic_auth: false,
        use_mutual_tls: false,
        proxy: {
          listen_path: "/auth/oidc/",
          target_url: `${this.callbackBaseUrl}/auth/oidc/callback`,
          strip_listen_path: true
        },
        active: true
      };

      const response = await axios.post(
        `${this.gatewayUrl}/tyk/apis`,
        oidcConfig,
        {
          headers: tykConfig.getGatewayHeaders(),
          timeout: 10000
        }
      );

      logger.info('Tyk OpenID Connect authentication initialized');
      return response.data;
    } catch (error) {
      logger.error('Failed to initialize Tyk OIDC:', error.message);
      throw error;
    }
  },

  /**
   * Initialize Azure AD authentication with Tyk
   */
  async initializeAzureAD() {
    if (!this.isEnabled) return null;

    try {
      const azureConfig = {
        name: "Azure AD Authentication",
        api_id: "azure-auth-api",
        org_id: tykConfig.orgId,
        use_keyless: false,
        use_oauth2: false,
        use_openid: true,
        openid_options: {
          providers: [
            {
              issuer: `https://login.microsoftonline.com/${config.azure.tenantID}/v2.0`,
              client_ids: {
                [config.azure.clientID]: config.azure.clientSecret
              }
            }
          ],
          segregate_by_client: false
        },
        enable_jwt: true,
        jwt_signing_method: "RS256",
        jwt_source: `https://login.microsoftonline.com/${config.azure.tenantID}/discovery/v2.0/keys`,
        jwt_identity_base_field: "sub",
        use_basic_auth: false,
        use_mutual_tls: false,
        proxy: {
          listen_path: "/auth/azure/",
          target_url: `${this.callbackBaseUrl}/auth/azure/callback`,
          strip_listen_path: true
        },
        active: true
      };

      const response = await axios.post(
        `${this.gatewayUrl}/tyk/apis`,
        azureConfig,
        {
          headers: tykConfig.getGatewayHeaders(),
          timeout: 10000
        }
      );

      logger.info('Tyk Azure AD authentication initialized');
      return response.data;
    } catch (error) {
      logger.error('Failed to initialize Tyk Azure AD:', error.message);
      throw error;
    }
  },

  /**
   * Handle SSO callback from Tyk
   */
  async handleSSOCallback(req, provider) {
    try {
      let userInfo = null;
      let identityId = null;

      // Extract user information based on provider
      switch (provider) {
        case 'saml':
          userInfo = this.extractSAMLUserInfo(req);
          break;
        case 'oauth':
          userInfo = this.extractOAuthUserInfo(req);
          break;
        case 'oidc':
          userInfo = this.extractOIDCUserInfo(req);
          break;
        case 'azure':
          userInfo = this.extractAzureUserInfo(req);
          break;
        default:
          throw new Error(`Unsupported SSO provider: ${provider}`);
      }

      if (!userInfo || !userInfo.email) {
        throw new Error('Unable to extract user information from SSO response');
      }

      // Find or create identity
      let identity = await Identity.findOne({
        where: { email: userInfo.email }
      });

      if (!identity) {
        // Create new identity for SSO user
        identity = await Identity.create({
          email: userInfo.email,
          first_name: userInfo.firstName || userInfo.given_name || '',
          last_name: userInfo.lastName || userInfo.family_name || '',
          identity_type: 1, // Regular user
          // Add other fields as needed
        });

        logger.info(`New identity created via Tyk SSO: ${identity.identity_id}`);
      }

      return identity;
    } catch (error) {
      logger.error('Tyk SSO callback handling failed:', error.message);
      throw error;
    }
  },

  /**
   * Extract user info from SAML response
   */
  extractSAMLUserInfo(req) {
    // Extract from Tyk SAML middleware headers or body
    return {
      email: req.headers['x-saml-email'] || req.body.email,
      firstName: req.headers['x-saml-first-name'] || req.body.firstName,
      lastName: req.headers['x-saml-last-name'] || req.body.lastName,
      nameID: req.headers['x-saml-name-id'] || req.body.nameID
    };
  },

  /**
   * Extract user info from OAuth response
   */
  extractOAuthUserInfo(req) {
    // Extract from OAuth token or user info endpoint
    const token = req.headers.authorization?.replace('Bearer ', '');
    // This would typically involve calling the OAuth provider's user info endpoint
    return {
      email: req.body.email || req.user?.email,
      firstName: req.body.given_name || req.user?.given_name,
      lastName: req.body.family_name || req.user?.family_name
    };
  },

  /**
   * Extract user info from OIDC JWT token
   */
  extractOIDCUserInfo(req) {
    // Extract from JWT claims
    const user = req.user || {};
    return {
      email: user.email,
      firstName: user.given_name,
      lastName: user.family_name,
      sub: user.sub
    };
  },

  /**
   * Extract user info from Azure AD token
   */
  extractAzureUserInfo(req) {
    // Extract from Azure AD JWT claims
    const user = req.user || {};
    return {
      email: user.email || user.preferred_username,
      firstName: user.given_name,
      lastName: user.family_name,
      sub: user.sub,
      tenantId: user.tid
    };
  },

  /**
   * Initialize all SSO providers
   */
  async initializeAllProviders() {
    if (!this.isEnabled) {
      logger.info('Tyk SSO not enabled, skipping initialization');
      return;
    }

    try {
      logger.info('Initializing Tyk SSO providers...');

      const results = await Promise.allSettled([
        this.initializeSAML(),
        this.initializeOAuth(),
        this.initializeOIDC(),
        this.initializeAzureAD()
      ]);

      results.forEach((result, index) => {
        const providers = ['SAML', 'OAuth', 'OIDC', 'Azure AD'];
        if (result.status === 'fulfilled') {
          logger.info(`${providers[index]} provider initialized successfully`);
        } else {
          logger.error(`${providers[index]} provider initialization failed:`, result.reason);
        }
      });

      logger.info('Tyk SSO providers initialization completed');
    } catch (error) {
      logger.error('Failed to initialize Tyk SSO providers:', error.message);
      throw error;
    }
  }
};

module.exports = tykSSO;
