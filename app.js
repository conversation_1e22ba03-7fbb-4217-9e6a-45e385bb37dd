const express = require("express");
const cors = require("cors");
const helmet = require("helmet");
const { status: httpStatus } = require("http-status");
const morgan = require("./config/morgan");
const passport = require('./config/passport');
const apiLimiter = require("./middlewares/rateLimiter");
const routes = require("./routes");
const { errorConverter, errorHandler } = require("./middlewares/error");
const { ApiError } = require("./helpers/api.helper");
const config = require("./config/config");
const tykConfig = require("./config/tyk");
const tykSSO = require("./config/tykSSO");
const logger = require("./config/logger");

const app = express();

/**
 * Initialize authentication system based on configuration
 */
const initializeAuth = async () => {
  if (config.auth.mode === 'tyk') {
    logger.info('Initializing Tyk authentication mode');

    try {
      // Validate Tyk configuration
      if (!tykConfig.isEnabled) {
        throw new Error('Tyk configuration is not properly set up');
      }

      // Initialize Tyk SSO providers
      await tykSSO.initializeAllProviders();

      logger.info('Tyk authentication initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize Tyk authentication:', error.message);
      logger.warn('Falling back to custom authentication mode');
      // Don't throw error, just log and continue with custom auth
    }
  } else {
    logger.info('Using custom authentication mode');
    // Initialize passport for custom authentication
    app.use(passport.initialize());
  }
};

// request logging
app.use(morgan.successHandler);
app.use(morgan.errorHandler);

// set security HTTP headers
app.use(helmet());
// parse json request body
app.use(express.json());

// parse urlencoded request body
app.use(express.urlencoded({ extended: true }));

// enable cors
app.use(cors());
app.options("*", cors());

// Initialize authentication system
initializeAuth().catch(error => {
  logger.error('Authentication initialization failed:', error.message);
});

// Conditionally initialize passport for custom auth mode
if (config.auth.mode !== 'tyk') {
  app.use(passport.initialize());
}

// limit repeated failed requests
app.use("/", apiLimiter);

// api routes
app.use("/", routes);

// Handle WebSocket endpoint requests (to prevent error logs)
app.all("/ws", (req, res) => {
  res.status(404).json({
    status: false,
    message: "WebSocket endpoint not available",
    note: "This application does not support WebSocket connections"
  });
});

// send back a 404 error for any unknown api request
app.use((req, res, next) => {
  next(new ApiError(httpStatus.NOT_FOUND, "Not found"));
});

// convert error to ApiError, if needed
app.use(errorConverter);

// handle error
app.use(errorHandler);

module.exports = app;
