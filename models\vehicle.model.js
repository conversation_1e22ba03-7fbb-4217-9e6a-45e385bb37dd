const history = require("../models/plugins/history.plugin");

module.exports = (sequelize, DataTypes) => {
  const Vehicle = sequelize.define(
    "Vehicle",
    {
      vehicle_id: {
        type: DataTypes.UUID,
        primaryKey: true,
        defaultValue: DataTypes.UUIDV4,
      },
      plate_number: {
        type: DataTypes.STRING(20),
        allowNull: false,
      },
      issued_by: {
        type: DataTypes.STRING(100),
        allowNull: true,
      },
      vin: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      year: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      make: {
        type: DataTypes.STRING(100),
        allowNull: true,
      },
      model: {
        type: DataTypes.STRING(100),
        allowNull: true,
      },
      color: {
        type: DataTypes.STRING(50),
        allowNull: true,
      },
      uploaded_date: {
        type: DataTypes.DATEONLY,
        allowNull: true,
      },
      identity_id: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: "identity",
          key: "identity_id",
        },
        onDelete: "CASCADE",
      },
      created_by: {
        type: DataTypes.UUID,
        allowNull: true,
      },
      updated_by: {
        type: DataTypes.UUID,
        allowNull: true,
      },
    },
    {
      tableName: "vehicle",
      timestamps: true,
      underscored: true,
      indexes: [
        {
          fields: ["identity_id"],
        },
        {
          fields: ["plate_number"],
          unique: true,
        },
        {
          fields: ["vin"],
        },
        {
          fields: ["make"],
        },
        {
          fields: ["model"],
        },
      ],
    }
  );

  Vehicle.associate = (models) => {
    Vehicle.belongsTo(models.Identity, {
      foreignKey: "identity_id",
      as: "identity",
    });
  };

  // Apply plugins
  history(Vehicle, sequelize, DataTypes);

  return Vehicle;
};
