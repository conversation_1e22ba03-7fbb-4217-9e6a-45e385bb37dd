module.exports = (sequelize, DataTypes) => {
  const Permission = sequelize.define(
    "Permission",
    {
      permission_id: {
        type: DataTypes.UUID,
        primaryKey: true,
        defaultValue: DataTypes.UUIDV4,
      },
      name: {
        type: DataTypes.STRING(50),
        allowNull: false,
        unique: true,
        set(value) {
          if (typeof value === "string") this.setDataValue("name", value.trim());
        },
      },
      description: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
    },
    {
      tableName: "permission",
      timestamps: true,
      underscored: true,
    }
  );

  Permission.associate = (models) => {
    // Many-to-many: Permission <-> Role via RolePermission mapping table
    Permission.belongsToMany(models.Role, {
      through: models.RolePermission,
      foreignKey: "permission_id",
      otherKey: "role_id",
      as: "role",
    });
  };

  return Permission;
};
