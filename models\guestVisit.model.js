const history = require("../models/plugins/history.plugin");

module.exports = (sequelize, DataTypes) => {
  const GuestVisit = sequelize.define(
    "GuestVisit",
    {
      guest_visit_id: {
        type: DataTypes.UUID,
        primaryKey: true,
        defaultValue: DataTypes.UUIDV4,
      },
      guest_id: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: "guest",
          key: "guest_id",
        },
        onDelete: "CASCADE",
        comment: "Reference to guest",
      },
      visit_id: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: "visit",
          key: "visit_id",
        },
        onDelete: "CASCADE",
        comment: "Reference to visit",
      },
      check_in_time: {
        type: DataTypes.DATE,
        allowNull: true,
        comment: "Actual check-in time of the guest",
      },
      check_out_time: {
        type: DataTypes.DATE,
        allowNull: true,
        comment: "Actual check-out time of the guest",
      },
      guest_status: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
      },
      guest_pin: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      screening: {
        type: DataTypes.BOOLEAN,
        allowNull: true,
        defaultValue: false,
      },
      created_by: {
        type: DataTypes.UUID,
        allowNull: true,
      },
      updated_by: {
        type: DataTypes.UUID,
        allowNull: true,
      },
    },
    {
      tableName: "guest_visit",
      timestamps: true,
      underscored: true,
      indexes: [
        {
          fields: ["guest_id"],
        },
        {
          fields: ["visit_id"],
        },
        {
          fields: ["guest_status"],
        },
        {
          fields: ["check_in_time"],
        },
        {
          fields: ["check_out_time"],
        },
        {
          fields: ["guest_id", "visit_id"],
          unique: true,
        },
      ],
    }
  );

  GuestVisit.associate = (models) => {
    // Belongs to Guest
    GuestVisit.belongsTo(models.Guest, {
      foreignKey: "guest_id",
      as: "guest",
    });

    GuestVisit.belongsTo(models.MasterData, {
      foreignKey: "guest_status",
      targetKey: "key",
      as: "guest_status_name",
      constraints: false,
      scope: {
        group: "guest_status",
      },
    });

    // Belongs to Visit
    GuestVisit.belongsTo(models.Visit, {
      foreignKey: "visit_id",
      as: "visit",
    });
  };

  // Apply plugins
  history(GuestVisit, sequelize, DataTypes);

  return GuestVisit;
};
