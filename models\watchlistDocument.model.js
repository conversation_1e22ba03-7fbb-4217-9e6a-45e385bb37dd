const { MEDIA } = require("../config/attributes");
const history = require("../models/plugins/history.plugin");
const media = require("../models/plugins/media.plugin");

module.exports = (sequelize, DataTypes) => {
  const WatchlistDocument = sequelize.define(
    "WatchlistDocument",
    {
      watchlist_document_id: {
        type: DataTypes.UUID,
        primaryKey: true,
        defaultValue: DataTypes.UUIDV4,
      },
      watchlist_id: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: "watchlist",
          key: "watchlist_id",
        },
        onDelete: "CASCADE",
      },
      name: {
        type: DataTypes.STRING(50),
        allowNull: false,
      },
      documents:{
        type:MEDIA,
        allowNull:true,
        allowMultiple:true,
      },
      updated_by: {
        type: DataTypes.UUID,
        allowNull: true,
      },
    },
    {
      tableName: "watchlist_document",
      timestamps: true,
      underscored: true,
    }
  );

  WatchlistDocument.associate = (models) => {
    WatchlistDocument.belongsTo(models.Watchlist, {
      foreignKey: "watchlist_id",
      as: "watchlist",
    });
  };

  // Apply plugins - media plugin should be applied before history
  media(WatchlistDocument, sequelize, DataTypes);
  history(WatchlistDocument, sequelize, DataTypes);

  return WatchlistDocument;
};