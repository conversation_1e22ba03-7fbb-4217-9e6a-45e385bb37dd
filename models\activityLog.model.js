module.exports = (sequelize, DataTypes) => {
    const ActivityLog = sequelize.define(
      "ActivityLog",
      {
        id: {
          type: DataTypes.UUID,
          primaryKey: true,
          defaultValue: DataTypes.UUIDV4,
        },
        identity_id: {
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: "identity",
            key: "identity_id",
          },
          onDelete: "CASCADE",
        },
        action: {
          type: DataTypes.ENUM("CUSTOM_LOGIN", "SAML_LOGIN", "OIDC_LOGIN", "AZURE_LOGIN", "LOGOUT", "REGISTER", "REFRESH_TOKEN"),
          allowNull: false,
        },
        metadata: {
          type: DataTypes.JSONB,
          defaultValue: {},
        },
        ip_address: {
          type: DataTypes.INET,
          allowNull: true,
        },
        user_agent: {
          type: DataTypes.TEXT,
          allowNull: true,
        },
      },
      {
        tableName: "activity_log",
        timestamps: true,
        underscored: true,
      }
    );
  
    ActivityLog.associate = (models) => {
      ActivityLog.belongsTo(models.Identity, {
        foreignKey: "identity_id",
        as: "identity",
        onDelete: "CASCADE",
      });
    };
  
    return ActivityLog;
  };
  