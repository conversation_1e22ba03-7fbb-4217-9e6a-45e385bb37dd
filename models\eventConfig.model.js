const history = require("../models/plugins/history.plugin");

module.exports = (sequelize, DataTypes) => {
  const EventConfig = sequelize.define(
    "EventConfig",
    {
      event_config_id: {
        type: DataTypes.UUID,
        primaryKey: true,
        defaultValue: DataTypes.UUIDV4,
      },
      event_name: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      event: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      queue: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      order: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      updated_by: {
        type: DataTypes.UUID,
        allowNull: true,
      },
    },
    {
      tableName: "event_config",
      timestamps: true,
      underscored: true,
      paranoid: true,
    }
  );

  history(EventConfig, sequelize, DataTypes);

  return EventConfig;
};
