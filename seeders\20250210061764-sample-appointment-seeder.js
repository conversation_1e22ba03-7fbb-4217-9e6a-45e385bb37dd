"use strict";
const { v4: uuidv4 } = require("uuid");
const { faker } = require("@faker-js/faker");
const logger = require("../config/logger");
module.exports = {
  up: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      const patients = await queryInterface.sequelize.query(
        "SELECT patient_id FROM patient;",
        { type: Sequelize.QueryTypes.SELECT }
      );
      const facilities = await queryInterface.sequelize.query(
        "SELECT facility_id FROM facility;",
        { type: Sequelize.QueryTypes.SELECT }
      );
      const function_name = await queryInterface.sequelize.query(
        "SELECT function_id FROM function where name='hl7Processor';",
        { type: Sequelize.QueryTypes.SELECT }
      );
      
      const appointments = Array.from({ length: 100 }).map(() => {
        const appointmentDate = new Date(); // Use the current date for appointment date
        const tenDaysLater = new Date(Date.now() + 3 * 24 * 60 * 60 * 1000);
        // Generate random times on the same day
        const randomHour = faker.number.int({ min: 8, max: 17 });
        const randomMinute = faker.number.int({ min: 0, max: 59 });
        const arrivalTime = new Date(Date.UTC(
          appointmentDate.getUTCFullYear(),
          appointmentDate.getUTCMonth(),
          appointmentDate.getUTCDate(),
          randomHour,
          randomMinute
        ));
        const departureTime = new Date(arrivalTime); // Start with arrival time
        departureTime.setUTCHours(departureTime.getUTCHours() + faker.number.int({ min: 1, max: 5 })); // Add 1 to 5 hours for departure time
        return {
          appointment_id: uuidv4(),
          hl7_appointment_id: faker.number.int({ min: 10000, max: 50000 }),
          patient_id: faker.helpers.arrayElement(patients).patient_id,
          appointment_date: faker.date.between({ from: appointmentDate, to: tenDaysLater }).toISOString(),
          department: faker.commerce.department(),
          provider_name: `${faker.person.firstName()} ${faker.person.lastName()}`,
          status: faker.helpers.arrayElement([0, 1, 2]),
          type: faker.helpers.arrayElement([0, 1]),
          room: faker.number.int({ min: 1, max: 500 }),
          beds: faker.number.int({ min: 1, max: 5 }), // Generates a random number of beds between 1 and 5
          facility_id: facilities.length > 0 ? faker.helpers.arrayElement(facilities).facility_id : null,
          arrival_time: arrivalTime.toISOString(), // Ensure UTC format
          departure_time: departureTime.toISOString(), // Ensure UTC format
          screening: false,
          created_at: new Date(),
          updated_at: new Date(),
          updated_by: function_name[0].function_id,
        };
      });
      await queryInterface.bulkInsert("appointment", appointments, { transaction });
      await transaction.commit();
    } catch (error) {
      logger.error("Seeding error:", error);
      await transaction.rollback();
      throw error;
    }
  },
  down: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.bulkDelete("appointment", null, { transaction });
      await transaction.commit();
    } catch (error) {
      logger.error("Rollback error:", error);
      await transaction.rollback();
      throw error;
    }
  },
};