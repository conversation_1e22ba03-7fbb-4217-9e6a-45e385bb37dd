#!/usr/bin/env node

/**
 * Migration Script: Custom to Tyk
 * Migrates existing roles and permissions to Tyk policy cache
 */

const { Role } = require('../models');
const tykPolicyManager = require('../utilities/tykPolicyManager');
const logger = require('../config/logger');
const config = require('../config/config');

const tykMigration = {
  /**
   * Run complete migration
   */
  async runMigration() {
    console.log('🚀 Starting migration from Custom to Tyk authentication...\n');

    try {
      // Pre-migration checks
      await this.preMigrationChecks();

      // Migrate roles to policy cache
      await this.migrateRolesToPolicies();

      console.log('✅ Migration completed successfully!');

    } catch (error) {
      console.error('❌ Migration failed:', error.message);
      logger.error('Migration failed:', error);
      process.exit(1);
    }
  },

  /**
   * Pre-migration checks
   */
  async preMigrationChecks() {
    console.log('🔍 Running pre-migration checks...');

    // Check if Tyk is configured
    if (config.auth.mode !== 'tyk' || !config.tyk.enabled) {
      throw new Error('AUTH_MODE must be set to "tyk" and TYK_ENABLED must be true for migration');
    }

    // Check database connectivity
    try {
      const roleCount = await Role.count();
      console.log(`📊 Found ${roleCount} roles to migrate to policy cache`);
    } catch (error) {
      throw new Error(`Database connectivity check failed: ${error.message}`);
    }

    console.log('✅ Pre-migration checks completed\n');
  },

  /**
   * Migrate roles to policy cache
   */
  async migrateRolesToPolicies() {
    console.log('📋 Migrating roles to policy cache...');

    try {
      const syncResults = await tykPolicyManager.syncAllRolesToTyk();
      
      const successCount = syncResults.filter(r => r.success).length;
      const failureCount = syncResults.filter(r => !r.success).length;

      console.log(`✅ Role migration completed: ${successCount} success, ${failureCount} failed`);

      if (failureCount > 0) {
        const failures = syncResults.filter(r => !r.success);
        console.log('❌ Failed migrations:');
        failures.forEach(f => console.log(`   - ${f.roleName}: ${f.error}`));
      }

    } catch (error) {
      console.error('❌ Failed to migrate roles:', error.message);
      throw error;
    }
  }
};

// Run migration if called directly
if (require.main === module) {
  tykMigration.runMigration()
    .then(() => {
      console.log('\n🎉 Migration process completed!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Migration process failed:', error.message);
      process.exit(1);
    });
}

module.exports = tykMigration;
