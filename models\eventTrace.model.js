module.exports = (sequelize, DataTypes) => {
    const EventTrace = sequelize.define(
      "EventTrace",
      {
        trace_id: {
          type: DataTypes.UUID,
          primaryKey: true,
          defaultValue: DataTypes.UUIDV4,
        },
        endpoint: {
          type: DataTypes.STRING,
          allowNull: true,
          comment: "If its filled then it means it is genrated from APIs service controller function"
        },
        function_id:{
          type: DataTypes.UUID,
          allowNull: true,
          comment: "If its filled then it means it is genrated from any processor function"
        }
      },
      {
        tableName: "event_trace",
        timestamps: true,
        createdAt: "created_at",
        updatedAt: false,
        underscored: true,
      }
    );
  
    return EventTrace;
  };
  