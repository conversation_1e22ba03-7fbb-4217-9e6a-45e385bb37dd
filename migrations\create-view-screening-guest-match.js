"use strict";

module.exports = {
  up: async (queryInterface, Sequel<PERSON>) => {
    return queryInterface.sequelize.query(`
CREATE VIEW view_guest_screening_matches AS
SELECT
  ags.appointment_guest_id,
  ags.appointment_guest_screening_id,
  agsm.match_id AS appointment_guest_screening_match_id,
  wl.first_name,
  wl.last_name,
  wl.date_of_birth AS dob,
  wl.gender,
  wl.hair_color,
  wl.created_by AS added_by,
  'Watchlist' AS match_type  -- Watchlist (Person of Interest)
FROM appointment_guest_screening_match agsm
JOIN appointment_guest_screening ags ON agsm.appointment_guest_screening_id = ags.appointment_guest_screening_id
JOIN appointment_guest ag ON ag.appointment_guest_id = ags.appointment_guest_id
JOIN watchlist wl ON agsm.match_name = wl.first_name || ' ' || wl.last_name

UNION

SELECT
  ags.appointment_guest_id,
  ags.appointment_guest_screening_id,
  agsm.match_id AS appointment_guest_screening_match_id,
  pg.first_name,
  pg.last_name,
  pg.birth_date AS dob,
  NULL AS gender,
  NULL AS hair_color,
  pg.created_by AS added_by,
  'Denied Guest' AS match_type  -- Denied Guest
FROM appointment_guest_screening_match agsm
JOIN appointment_guest_screening ags ON agsm.appointment_guest_screening_id = ags.appointment_guest_screening_id
JOIN appointment_guest ag ON ag.appointment_guest_id = ags.appointment_guest_id
JOIN patient_guest pg ON agsm.match_name = pg.first_name || ' ' || pg.last_name AND pg.guest_type = 2;
    `);
  },

  down: async (queryInterface, Sequelize) => {
    return queryInterface.sequelize.query(`
      DROP VIEW IF EXISTS view_guest_screening_matches;
    `);
  },
};
