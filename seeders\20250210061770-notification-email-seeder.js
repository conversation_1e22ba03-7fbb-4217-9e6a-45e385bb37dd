"use strict";
const { v4: uuidv4 } = require("uuid");

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.bulkInsert('notification_email', [
      {
        notification_email_id: uuidv4(),
        name: 'Send Check In notification',
        subject: "Your guest is checking-in",
        template: `<!DOCTYPE html>
                    <html lang="en">
                    <head>
                      <meta charset="UTF-8" />
                      <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
                      <title>Hello, <%= patient_full_name || "Patient User" %></title>
                      <style>
                        /* Reset some defaults */
                        body, table, td, a { -webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; }
                        table { border-collapse: collapse !important; }
                        img { border: 0; line-height: 100%; outline: none; text-decoration: none; }
                        body { margin: 0 !important; padding: 0 !important; width: 100% !important; }

                        /* Container */
                        .email-container {
                          width: 100%;
                          max-width: 600px;
                          margin: 0 auto;
                          background-color: #ffffff;
                          font-family: ‘Helvetica Neue', Helvetica, Arial, sans-serif;
                          color: #333333;
                          border: 1px solid #dddddd;
                          border-radius: 4px;
                          overflow: hidden;
                        }

                        /* Header */
                        .header {
                          background-color: #0073e6;
                          padding: 20px;
                          text-align: center;
                          color: #ffffff;
                          font-size: 24px;
                          font-weight: bold;
                        }

                        /* Body */
                        .body {
                          padding: 30px;
                          font-size: 16px;
                          line-height: 1.5;
                        }

                        .body p {
                          margin-bottom: 20px;
                        }

                        /* Footer */
                        .footer {
                          padding: 15px 30px;
                          font-size: 12px;
                          color: #777777;
                          text-align: center;
                          background-color: #f9f9f9;
                        }
                      </style>
                    </head>
                    <body>
                      <table role="presentation" width="100%" cellpadding="0" cellspacing="0">
                        <tr>
                          <td align="center">
                            <table role="presentation" class="email-container">
                              <!-- Header -->
                              <tr>
                                <td class="header">
                                  Hello, <%= guest_full_name %>!
                                </td>
                              </tr>

                              <!-- Body -->
                              <tr>
                                <td class="body">
                                  <p>
                                    We're informing to let you know that you are going to visit your patient,
                                    <strong><%= patient_full_name %></strong>
                                  </p>

                                  <p>
                                    If you have any questions or need assistance, just reach out to our support team.
                                  </p>

                                  <p>Thanks,<br/>
                                  The Care Team</p>
                                </td>
                              </tr>

                              <!-- Footer -->
                              <tr>
                                <td class="footer">
                                  Please do not reply to this email. This inbox is not monitored.
                                </td>
                              </tr>
                            </table>
                          </td>
                        </tr>
                      </table>
                    </body>
                    </html>
                    `,
        status: 'Active',
        language: 'en-US',
        receiver_column: 'email',
        created_at: new Date(),
        updated_at: new Date(),
      },
      {
        notification_email_id: uuidv4(),
        name: 'Identity Created Notification',
        subject: "New Identity Created",
        template: `<!DOCTYPE html>
                    <html lang="en">
                    <head>
                      <meta charset="UTF-8" />
                      <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
                      <title>New Identity Created</title>
                      <style>
                        body, table, td, a { -webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; }
                        table { border-collapse: collapse !important; }
                        img { border: 0; line-height: 100%; outline: none; text-decoration: none; }
                        body { margin: 0 !important; padding: 0 !important; width: 100% !important; }

                        .email-container {
                          width: 100%;
                          max-width: 600px;
                          margin: 0 auto;
                          background-color: #ffffff;
                          font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
                          color: #333333;
                          border: 1px solid #dddddd;
                          border-radius: 4px;
                          overflow: hidden;
                        }

                        .header {
                          background-color: #0073e6;
                          padding: 20px;
                          text-align: center;
                          color: #ffffff;
                          font-size: 24px;
                          font-weight: bold;
                        }

                        .body {
                          padding: 30px;
                          font-size: 16px;
                          line-height: 1.5;
                        }

                        .body p {
                          margin-bottom: 20px;
                        }

                        .footer {
                          padding: 15px 30px;
                          font-size: 12px;
                          color: #777777;
                          text-align: center;
                          background-color: #f9f9f9;
                        }
                      </style>
                    </head>
                    <body>
                      <table role="presentation" width="100%" cellpadding="0" cellspacing="0">
                        <tr>
                          <td align="center">
                            <table role="presentation" class="email-container">
                              <tr>
                                <td class="header">
                                  New Identity Created
                                </td>
                              </tr>
                              <tr>
                                <td class="body">
                                  <p>
                                    A new identity has been created for <strong><%= first_name %> <%= last_name %></strong>
                                  </p>
                                  <p>
                                    <strong>Email:</strong> <%= email %><br/>
                                    <strong>Employee ID:</strong> <%= eid %><br/>
                                    <strong>Company:</strong> <%= company %><br/>
                                    <strong>Job Title:</strong> <%= job_title %>
                                  </p>
                                  <p>Thanks,<br/>
                                  The Care Team</p>
                                </td>
                              </tr>
                              <tr>
                                <td class="footer">
                                  Please do not reply to this email. This inbox is not monitored.
                                </td>
                              </tr>
                            </table>
                          </td>
                        </tr>
                      </table>
                    </body>
                    </html>`,
        status: 'Active',
        language: 'en-US',
        receiver_column: 'email',
        created_at: new Date(),
        updated_at: new Date(),
      },
      {
        notification_email_id: uuidv4(),
        name: 'Identity Updated Notification',
        subject: "Identity Information Updated",
        template: `<!DOCTYPE html>
                    <html lang="en">
                    <head>
                      <meta charset="UTF-8" />
                      <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
                      <title>Identity Updated</title>
                      <style>
                        body, table, td, a { -webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; }
                        table { border-collapse: collapse !important; }
                        img { border: 0; line-height: 100%; outline: none; text-decoration: none; }
                        body { margin: 0 !important; padding: 0 !important; width: 100% !important; }

                        .email-container {
                          width: 100%;
                          max-width: 600px;
                          margin: 0 auto;
                          background-color: #ffffff;
                          font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
                          color: #333333;
                          border: 1px solid #dddddd;
                          border-radius: 4px;
                          overflow: hidden;
                        }

                        .header {
                          background-color: #28a745;
                          padding: 20px;
                          text-align: center;
                          color: #ffffff;
                          font-size: 24px;
                          font-weight: bold;
                        }

                        .body {
                          padding: 30px;
                          font-size: 16px;
                          line-height: 1.5;
                        }

                        .body p {
                          margin-bottom: 20px;
                        }

                        .footer {
                          padding: 15px 30px;
                          font-size: 12px;
                          color: #777777;
                          text-align: center;
                          background-color: #f9f9f9;
                        }
                      </style>
                    </head>
                    <body>
                      <table role="presentation" width="100%" cellpadding="0" cellspacing="0">
                        <tr>
                          <td align="center">
                            <table role="presentation" class="email-container">
                              <tr>
                                <td class="header">
                                  Identity Information Updated
                                </td>
                              </tr>
                              <tr>
                                <td class="body">
                                  <p>
                                    The identity information for <strong><%= first_name %> <%= last_name %></strong> has been updated.
                                  </p>
                                  <p>
                                    <strong>Email:</strong> <%= email %><br/>
                                    <strong>Employee ID:</strong> <%= eid %><br/>
                                    <strong>Company:</strong> <%= company %><br/>
                                    <strong>Job Title:</strong> <%= job_title %>
                                  </p>
                                  <p>Thanks,<br/>
                                  The Care Team</p>
                                </td>
                              </tr>
                              <tr>
                                <td class="footer">
                                  Please do not reply to this email. This inbox is not monitored.
                                </td>
                              </tr>
                            </table>
                          </td>
                        </tr>
                      </table>
                    </body>
                    </html>`,
        status: 'Active',
        language: 'en-US',
        receiver_column: 'email',
        created_at: new Date(),
        updated_at: new Date(),
      },
    ]);
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.bulkDelete('notification_email', null, {});
  },
};