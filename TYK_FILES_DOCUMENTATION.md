# Tyk Integration Files Documentation

## 🎯 Overview
This document explains all Tyk-related files and their purposes in the CareMate API integration.

## 📁 Core Configuration Files

### `config/tyk.js`
**Purpose**: Main Tyk configuration and API definition generation
**Architecture**: Simple object with methods (singleton pattern)
**What it does**:
- Validates Tyk environment variables
- Provides headers for Tyk Gateway API calls
- Generates API definitions for Tyk Gateway
- Generates policy definitions for roles/permissions
- **Used by**: All Tyk services and middlewares
- **Key Methods**: `validateConfig()`, `getGatewayHeaders()`, `generateApiDefinition()`

### `config/config.js` (Updated)
**Purpose**: Environment variable validation and configuration
**What was added**:
- `AUTH` variable validation (custom/tyk)
- All Tyk environment variables (TYK_GATEWAY_URL, TYK_SECRET, TYK_CALLBACK_BASE_URL, etc.)
- Conditional validation (required only when AUTH=tyk)
- **Used by**: Entire application for configuration

### `config/tykSSO.js`
**Purpose**: SSO integration through Tyk Gateway
**Architecture**: Simple object with methods (singleton pattern)
**What it does**:
- Replaces passport strategies when AUTH=tyk
- Configures SAML, OAuth, OIDC, Azure AD through Tyk Gateway
- Handles SSO callbacks and user extraction
- Uses configurable callback URLs from environment
- **Used by**: Authentication flows when SSO is needed
- **Key Methods**: `initializeSAML()`, `initializeOAuth()`, `handleSSOCallback()`

## 🔐 Authentication & Authorization Files

### `middlewares/tykAuth.js`
**Purpose**: Tyk authentication middleware
**What it does**:
- Validates JWT tokens using Tyk secret
- Introspects tokens with Tyk Gateway
- Checks user permissions against database
- **Used by**: Protected routes when AUTH=tyk

### `middlewares/auth.js` (Updated)
**Purpose**: Main authentication middleware with dual mode support
**What was changed**:
- Added conditional logic to use tykAuth when AUTH=tyk
- Maintains existing custom auth when AUTH=custom
- **Used by**: All protected routes in the application

### `services/tyk.service.js`
**Purpose**: Tyk Gateway API service layer
**Architecture**: Simple object with methods (singleton pattern)
**What it does**:
- Creates/updates API definitions in Tyk Gateway
- Generates JWT tokens for Tyk authentication
- Health checks for Tyk Gateway
- **Removed**: Dashboard-dependent features (policies, API keys, analytics)
- **Used by**: Controllers, policy manager, authentication flows
- **Key Methods**: `generateTykJWT()`, `validateJWT()`, `checkGatewayHealth()`

## 🛠️ Policy & User Management Files

### `utilities/tykPolicyManager.js`
**Purpose**: Dynamic policy management for roles/permissions
**Architecture**: Simple object with methods (singleton pattern)
**What it does**:
- Caches role-to-policy mappings in memory
- Syncs permission changes to policy cache
- Manages rate limits and quotas per role
- **Simplified**: No direct Tyk API calls, works with cache only
- **Used by**: When roles/permissions change, authentication flows
- **Key Methods**: `cacheRolePolicy()`, `syncAllRolesToTyk()`, `getCachedPolicy()`

### `controllers/auth.controller.js` (Updated)
**Purpose**: Authentication controller with Tyk support
**What was changed**:
- Login/register methods support both auth modes
- Generates Tyk tokens when AUTH=tyk
- Creates Tyk policies for new users
- SSO callback handling for both modes
- **Used by**: Authentication routes (/auth/login, /auth/register, etc.)

## 🔄 Migration & Sync Files

### `scripts/migrate-to-tyk.js`
**Purpose**: One-time migration from custom to Tyk authentication
**What it does**:
- Migrates all existing roles to policy cache
- Validates migration success
- Provides rollback capability
- **Simplified**: Works with cache instead of direct Tyk API calls
- **Used by**: Manual execution when switching to Tyk mode

### `scripts/sync-tyk-policies.js`
**Purpose**: Ongoing synchronization of roles/policies
**What it does**:
- Syncs role changes to policy cache
- Cleans up orphaned cached policies
- Validates sync status
- **Simplified**: Cache-based operations only
- **Used by**: Scheduled tasks or manual execution

## 📊 Logging & Monitoring Files

### `config/morgan.js` (Updated)
**Purpose**: HTTP request logging with Tyk analytics
**What was changed**:
- Added Tyk analytics integration
- Sends request data to Tyk when AUTH=tyk
- Enhanced log format with auth mode and identity
- **Used by**: All HTTP requests for logging

### `config/logger.js` (Updated)
**Purpose**: Application logging with Tyk transport
**What was changed**:
- Added custom Tyk transport for logs
- Sends application logs to Tyk
- Enhanced logging methods for Tyk-specific events
- **Used by**: Entire application for logging

### `middlewares/rateLimiter.js` (Updated)
**Purpose**: Rate limiting with Tyk integration
**What was changed**:
- Uses Tyk rate limiting when AUTH=tyk
- Falls back to custom rate limiting if Tyk fails
- Checks rate limits with Tyk Gateway
- **Used by**: All API routes for rate limiting

## 🚨 Error Handling Files

### `helpers/tykError.helper.js`
**Purpose**: Comprehensive error handling for Tyk services
**What it does**:
- Defines Tyk-specific error types
- Handles connection, auth, rate limit errors
- Provides retry logic and circuit breaker
- **Used by**: All Tyk service calls

### `middlewares/error.js` (Updated)
**Purpose**: Global error handler with Tyk error support
**What was changed**:
- Added Tyk error handling before general errors
- Provides fallback mechanisms for Tyk failures
- **Used by**: All application errors

## 📦 Package & App Files

### `package.json` (Updated)
**Purpose**: Dependencies and scripts
**What was added**:
- `axios` dependency for Tyk Gateway API calls
- Tyk management scripts (migrate, sync)
- **Removed**: Analytics and dashboard-related scripts
- **Used by**: npm commands and application startup

### `app.js` (Updated)
**Purpose**: Application bootstrap with Tyk initialization
**What was changed**:
- Conditional Tyk SSO initialization
- Passport initialization only for custom mode
- **Simplified**: No dashboard or analytics initialization
- **Used by**: Application startup

### `.env.local.example`
**Purpose**: Environment configuration template
**What it contains**:
- All required Tyk environment variables
- Callback base URL configuration
- SSO provider configurations
- **Used by**: Development setup and deployment

## 🎯 File Usage Summary

### **Always Used (Both Modes)**:
- `config/config.js` - Configuration validation
- `middlewares/auth.js` - Route protection
- `controllers/auth.controller.js` - Authentication endpoints

### **Only Used When AUTH=tyk**:
- `config/tyk.js` - Tyk configuration (simple object)
- `middlewares/tykAuth.js` - Tyk authentication
- `services/tyk.service.js` - Tyk Gateway API calls (simple object)
- `utilities/tykPolicyManager.js` - Policy cache management (simple object)
- `config/tykSSO.js` - SSO integration (simple object)

### **Migration/Maintenance Only**:
- `scripts/migrate-to-tyk.js` - One-time migration (simplified)
- `scripts/sync-tyk-policies.js` - Ongoing sync (cache-based)

### **Enhanced for Tyk**:
- `config/morgan.js` - Dual logging
- `config/logger.js` - Tyk transport
- `middlewares/rateLimiter.js` - Dual rate limiting
- `helpers/tykError.helper.js` - Error handling
- `middlewares/error.js` - Error processing

### **Configuration Files**:
- `.env.local.example` - Environment template with all Tyk variables

## 🔧 How They Work Together

1. **Startup**: `app.js` reads `config/config.js` and initializes based on AUTH mode
2. **Authentication**: `middlewares/auth.js` routes to either custom or `middlewares/tykAuth.js`
3. **Tyk Calls**: `services/tyk.service.js` handles Tyk Gateway API interactions
4. **Policy Management**: `utilities/tykPolicyManager.js` manages policy cache
5. **Error Handling**: `helpers/tykError.helper.js` manages Tyk-specific errors
6. **Logging**: Enhanced loggers send data to both local and Tyk systems
7. **SSO**: `config/tykSSO.js` handles SSO through Tyk Gateway

## 💡 Key Benefits

- **Simple Architecture**: Objects with methods instead of complex classes
- **Singleton Pattern**: Single instance across app with direct access
- **No Dashboard Dependency**: Works with Tyk Gateway only
- **Configurable URLs**: All URLs come from environment variables
- **Modular**: Each file has a specific purpose
- **Conditional**: Tyk files only load when needed
- **Backward Compatible**: Custom auth continues to work
- **Maintainable**: Clear separation of concerns
- **Cache-Based**: Fast policy lookups without API calls

## 🚀 Quick Setup

1. **Copy environment file**: `cp .env.local.example .env.local`
2. **Configure Tyk variables**: Update TYK_* variables in .env.local
3. **Set auth mode**: `AUTH=tyk` in .env.local
4. **Install dependencies**: `npm install`
5. **Start application**: `npm start`

## 📝 Environment Variables Required

```bash
# Core Tyk Configuration
AUTH=tyk
TYK_GATEWAY_URL=http://localhost:8080
TYK_SECRET=your-gateway-secret
TYK_API_KEY=your-api-key
TYK_ORG_ID=your-org-id
TYK_LISTEN_PATH=/api/v1
TYK_TARGET_URL=http://localhost:3001
TYK_CALLBACK_BASE_URL=http://localhost:3000
```
