const history = require("../models/plugins/history.plugin");

module.exports = (sequelize, DataTypes) => {
  const OutPatientCheckin = sequelize.define(
    "OutPatientCheckin",
    {
      outpatient_checkin_id: {
        type: DataTypes.UUID,
        primaryKey: true,
        defaultValue: DataTypes.UUIDV4,
      },
      appointment_id: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: "appointment",
          key: "appointment_id",
        },
        onDelete: "CASCADE",
      },
      checkin_time: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      checkout_time: {
        type: DataTypes.DATE,
        allowNull: true,
      },
    },
    {
      tableName: "outpatient_checkin",
      timestamps: true,
      underscored: true,
    }
  );

  OutPatientCheckin.associate = (models) => {
    OutPatientCheckin.belongsTo(models.Appointment, {
      foreignKey: "appointment_id",
      as: "appointment",
      onDelete: "CASCADE",
    });
  };

  history(OutPatientCheckin, sequelize, DataTypes);

  return OutPatientCheckin;
};
