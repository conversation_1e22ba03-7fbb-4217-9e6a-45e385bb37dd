"use strict";
const { v4: uuidv4 } = require("uuid");
const { faker } = require("@faker-js/faker");

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      // Fetch references
      const timezones = await queryInterface.sequelize.query(
        "SELECT timezone_id FROM timezone LIMIT 5;",
        { type: Sequelize.QueryTypes.SELECT, transaction }
      );
      const countries = await queryInterface.sequelize.query(
        "SELECT country_id FROM country LIMIT 5;",
        { type: Sequelize.QueryTypes.SELECT, transaction }
      );
      const states = await queryInterface.sequelize.query(
        "SELECT state_id, country_id FROM state LIMIT 5;",
        { type: Sequelize.QueryTypes.SELECT, transaction }
      );

      // Prepare facilities and addresses
      const facilities = [];
      const addresses = [];

      for (let i = 0; i < 5; i++) {
        const facility_id = uuidv4();
        const timezone = timezones.length > 0 ? faker.helpers.arrayElement(timezones) : {};
        const state = states.length > 0 ? faker.helpers.arrayElement(states) : {};
        const country_id = state.country_id || (countries.length > 0 ? faker.helpers.arrayElement(countries).country_id : null);

        facilities.push({
          facility_id,
          name: `Facility ${faker.company.name()} ${i + 1}`,
          image: null,
          facility_code: faker.string.alphanumeric(6),
          facility_type: faker.helpers.arrayElement([0, 1, 2, 3, 4]),
          timezone_id: timezone.timezone_id || null,
          phone: faker.phone.number().slice(0, 20),
          email: faker.internet.email(),
          geo_location_code: faker.location.longitude({ min: -180, max: 180, precision: 7 }),
          other_code: faker.string.alphanumeric(8),
          status: faker.helpers.arrayElement([0, 1]),
          facility_url: faker.internet.url(),
          connected_applications: null,
          notes: faker.lorem.sentence(),
          updated_by: null,
          created_at: new Date(),
          updated_at: new Date(),
        });

        addresses.push({
          address_id: uuidv4(),
          facility_id,
          address_line_1: faker.location.streetAddress(),
          address_line_2: faker.location.secondaryAddress(),
          postal_code: faker.location.zipCode(),
          region: faker.location.state(),
          country_id: country_id,
          state_id: state.state_id || null,
          updated_by: null,
          created_at: new Date(),
          updated_at: new Date(),
        });
      }

      await queryInterface.bulkInsert("facility", facilities, { transaction });
      await queryInterface.bulkInsert("address", addresses, { transaction });
      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.bulkDelete("address", null, {});
    await queryInterface.bulkDelete("facility", null, {});
  },
};
