"use strict";
const { v4: uuidv4 } = require("uuid");

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // Fetch the application type IDs for the required types
    const [appTypes] = await queryInterface.sequelize.query(
      `SELECT application_type_id FROM application_type WHERE name = 'processor';`
    );


    const queueFunctions = [
      {
        function_id: uuidv4(),
        queue: "hl7_queue",
        type: "1",
        name: "hl7Processor",
        display_name: "HL7 Processor",
        application_type_id: appTypes[0]["application_type_id"],
        created_at: new Date(),
        updated_at: new Date(),
      },
      {
        function_id: uuidv4(),
        queue: "hl7_store",
        type: "1",
        name: "hl7Store",
        display_name: "HL7 Raw Storage",
        application_type_id: appTypes[0]["application_type_id"],
        created_at: new Date(),
        updated_at: new Date(),
      },
      {
        function_id: uuidv4(),
        queue: "patient_admission_procesor",
        type: "0",
        name: "addDefaultG<PERSON>",
        display_name: "Add default guests of patient",
        application_type_id: appTypes[0]["application_type_id"],
        created_at: new Date(),
        updated_at: new Date(),
      },
      {
        function_id: uuidv4(),
        queue: "notification_queue",
        type: "0",
        name: "generateNotification",
        display_name: "Generate Notification",
        application_type_id: appTypes[0]["application_type_id"],
        created_at: new Date(),
        updated_at: new Date(),
      },
      {
        function_id: uuidv4(),
        queue: "email_queue",
        type: "0",
        name: "sendEmail",
        display_name: "Send Email",
        application_type_id: appTypes[0]["application_type_id"],
        created_at: new Date(),
        updated_at: new Date(),
      },
      {
        function_id: uuidv4(),
        queue: "text_queue",
        type: "0",
        name: "sendText",
        display_name: "Send Text",
        application_type_id: appTypes[0]["application_type_id"],
        created_at: new Date(),
        updated_at: new Date(),
      },
      {
        function_id: uuidv4(),
        queue: "hr_csv_data",
        type: "1",
        name: "parseData",
        display_name: "Parse HR Data",
        description: "Parse HR data and map it in DB",
        application_type_id: appTypes[0]["application_type_id"],
        created_at: new Date(),
        updated_at: new Date(),
      },
    ];
    

    await queryInterface.bulkInsert("function", queueFunctions, {});
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.bulkDelete("function", null, {});
  },
};
