const history = require("../models/plugins/history.plugin");

module.exports = (sequelize, DataTypes) => {
  const IdentityRole = sequelize.define(
    "IdentityRole",
    {
      identity_role_id: {
        type: DataTypes.UUID,
        primaryKey: true,
        defaultValue: DataTypes.UUIDV4,
      },
      identity_id: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: "identity",
          key: "identity_id",
        },
      },
      role_id: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: "role",
          key: "role_id",
        },
      },
      updated_by: {
        type: DataTypes.UUID,
        allowNull: true,
      },
    },
    {
      tableName: "identity_role",
      timestamps: true,
      underscored: true,
      indexes: [
        {
          unique: true,
          fields: ["identity_id", "role_id"],
        },
      ],
    }
  );

  IdentityRole.associate = (models) => {
    IdentityRole.belongsTo(models.Identity, {
      foreignKey: "identity_id",
      as: "identity",
      onDelete: "CASCADE",
    });
    IdentityRole.belongsTo(models.Role, {
      foreignKey: "role_id",
      as: "role",
      onDelete: "CASCADE",
    });
  };

  history(IdentityRole, sequelize, DataTypes);

  return IdentityRole;
};
