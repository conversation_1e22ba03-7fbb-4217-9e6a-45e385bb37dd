const history = require("../models/plugins/history.plugin");

module.exports = (sequelize, DataTypes) => {
  const NdaSignature = sequelize.define(
    "NdaSignature",
    {
      nda_signature_id: {
        type: DataTypes.UUID,
        primaryKey: true,
        defaultValue: DataTypes.UUIDV4,
      },
      nda_agreement_id: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: "nda_agreement",
          key: "nda_agreement_id",
        },
        onDelete: "CASCADE",
      },
      signer_role: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      signer_id: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: "identity",
          key: "identity_id",
        },
        onDelete: "CASCADE",
      },
      signed_at: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      signature_method: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      signature: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
    },
    {
      tableName: "nda_signature",
      timestamps: true,
      underscored: true,
    }
  );

  NdaSignature.associate = (models) => {
    NdaSignature.belongsTo(models.NdaAgreement, {
      foreignKey: "nda_agreement_id",
      as: "agreement",
    });

    NdaSignature.belongsTo(models.Identity, {
      foreignKey: "signer_id",
      as: "identity",
    });
    NdaSignature.belongsTo(models.MasterData, {
      foreignKey: "signer_role",
      targetKey: "key",
      as: "signer_role_name",
      constraints: false,
      scope: {
        group: "signer_role",
      },
    });
    NdaSignature.belongsTo(models.MasterData, {
      foreignKey: "signature_method",
      targetKey: "key",
      as: "signature_method_name",
      constraints: false,
      scope: {
        group: "signature_method",
      },
    });
  };

  history(NdaSignature, sequelize, DataTypes);

  return NdaSignature;
};
