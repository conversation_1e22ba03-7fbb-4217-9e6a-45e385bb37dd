const { template } = require("lodash");
const history = require("../models/plugins/history.plugin");

module.exports = (sequelize, DataTypes) => {
  const Card = sequelize.define(
    "Card",
    {
      card_id: {
        type: DataTypes.UUID,
        primaryKey: true,
        defaultValue: DataTypes.UUIDV4,
      },
      identity_id: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: "identity",
          key: "identity_id",
        },
        onDelete: "CASCADE",
      },
      card_number: {
        type: DataTypes.STRING(50),
        allowNull: false,
        unique: true,
      },
      card_format: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      facility_code: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      pin: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      template: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      active_date: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      deactive_date: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      reason: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      status: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      updated_by: {
        type: DataTypes.UUID,
        allowNull: true,
      },
    },
    {
      tableName: "card",
      timestamps: true,
      underscored: true,
    }
  );

  Card.associate = (models) => {
    Card.belongsTo(models.Identity, {
      foreignKey: "identity_id",
      as: "identity",
    });
    Card.belongsTo(models.MasterData, {
      foreignKey: "card_format",
      targetKey: "key",
      as: "card_format_name",
      constraints: false,
      scope: {
        group: "card_format",
      },
    });
    Card.belongsTo(models.MasterData, {
      foreignKey: "template",
      targetKey: "key",
      as: "card_template_name",
      constraints: false,
      scope: {
        group: "card_template",
      },
    });
    Card.belongsTo(models.MasterData, {
      foreignKey: "status",
      targetKey: "key",
      as: "card_status_name",
      constraints: false,
      scope: {
        group: "card_status",
      },
    });
  };

  history(Card, sequelize, DataTypes);

  return Card;
};
