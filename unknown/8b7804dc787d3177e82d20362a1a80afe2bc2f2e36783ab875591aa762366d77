const history = require("../models/plugins/history.plugin");

module.exports = (sequelize, DataTypes) => {
  const AppointmentGuestScreeningMatch = sequelize.define(
    "AppointmentGuestScreeningMatch",
    {
      appointment_guest_screening_id: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: "appointment_guest_screening",
          key: "appointment_guest_screening_id",
        },
        onDelete: "CASCADE",
      },
      match_id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
      },
      match_name: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      type: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
    },
    {
      tableName: "appointment_guest_screening_match",
      timestamps: true,
      underscored: true,
    }
  );

  AppointmentGuestScreeningMatch.associate = (models) => {
    AppointmentGuestScreeningMatch.belongsTo(models.AppointmentGuestScreening, {
      foreignKey: "appointment_guest_screening_id",
      as: "appointmentGuestScreening",
      onDelete: "CASCADE",
    });
    AppointmentGuestScreeningMatch.belongsTo(models.MasterData, {
      foreignKey: "type",
      targetKey: "key",
      as: "screening_match_type_name",
      constraints: false,
      scope: {
        group: "screening_match_type",
      },
    });
  };

  history(AppointmentGuestScreeningMatch, sequelize, DataTypes);

  return AppointmentGuestScreeningMatch;
};
