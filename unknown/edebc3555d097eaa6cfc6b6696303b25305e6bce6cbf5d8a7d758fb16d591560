module.exports = (sequelize, DataTypes) => {
    const PatientHistoryView = sequelize.define(
      "PatientHistoryView",
      { 
        patient_id:{
        type: DataTypes.UUID,
        primaryKey: true,
        allowNull: false,
       },
        effective_date: {
          type: DataTypes.DATE,
          allowNull: false,
        },
        patient_history_id: {
          type: DataTypes.UUID,
          primaryKey: true,
          allowNull: false,
        },
        field_changes: {
          type: DataTypes.STRING,
          allowNull: false,
        },
        old_value: {
          type: DataTypes.STRING,
          allowNull: true,
        },
        new_value: {
          type: DataTypes.STRING,
          allowNull: true,
        },
        event_type: {
          type: DataTypes.STRING,
          allowNull: false,
        },
        modified_by: {
          type: DataTypes.UUID,
          allowNull: true,
        },
      },
      {
        tableName: "view_patient_history",
        timestamps: false,
        underscored: true,
      }
    );
  
    return PatientHistoryView;
  };
  