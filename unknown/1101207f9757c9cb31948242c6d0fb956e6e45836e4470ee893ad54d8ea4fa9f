'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    return queryInterface.sequelize.query(`
      CREATE VIEW view_watchlist_history AS
      SELECT
        h.created_at AS effective_date,
        h.watchlist_history_id,
        h.column_name AS field_changes,
        h.old_value,
        h.new_value,
        t.operation AS event_type,
        t.watchlist_id AS watchlist_id,
        t.updated_by AS modified_by
      FROM watchlist_history h
      JOIN watchlist_transaction t
        ON h.watchlist_transaction_id = t.watchlist_transaction_id;
    `);
  },

  down: async (queryInterface, Sequelize) => {
    return queryInterface.sequelize.query(
      `DROP VIEW IF EXISTS view_watchlist_history;`
    );
  },
};
