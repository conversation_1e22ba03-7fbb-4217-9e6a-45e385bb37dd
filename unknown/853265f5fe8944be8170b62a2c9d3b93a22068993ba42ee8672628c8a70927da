"use strict";
const { v4: uuidv4 } = require("uuid");

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // Fetch the notification
    const [notifications] = await queryInterface.sequelize.query(`
      SELECT notification_id FROM notification
      WHERE name = 'Send Check In notification'
      LIMIT 1;
    `);
    const notification = notifications[0];

    // Fetch the email template
    const [emailTemplates] = await queryInterface.sequelize.query(`
      SELECT notification_email_id FROM notification_email
      WHERE name = 'Send Check In notification'
      LIMIT 1;
    `);
    const emailTemplate = emailTemplates[0];

    // Fetch the text template
    const [textTemplates] = await queryInterface.sequelize.query(`
      SELECT notification_text_id FROM notification_text
      WHERE name = 'Send Check In notification'
      LIMIT 1;
    `);
    const textTemplate = textTemplates[0];

    if (!notification || !emailTemplate || !textTemplate) {
      throw new Error("Missing required data for NotificationChannel seeder.");
    }

    // Fetch identity notifications
    const [identityCreatedNotifications] = await queryInterface.sequelize.query(`
      SELECT notification_id FROM notification
      WHERE name = 'Identity Created Notification'
      LIMIT 1;
    `);
    const identityCreatedNotification = identityCreatedNotifications[0];

    const [identityUpdatedNotifications] = await queryInterface.sequelize.query(`
      SELECT notification_id FROM notification
      WHERE name = 'Identity Updated Notification'
      LIMIT 1;
    `);
    const identityUpdatedNotification = identityUpdatedNotifications[0];

    // Fetch identity email templates
    const [identityCreatedEmailTemplates] = await queryInterface.sequelize.query(`
      SELECT notification_email_id FROM notification_email
      WHERE name = 'Identity Created Notification'
      LIMIT 1;
    `);
    const identityCreatedEmailTemplate = identityCreatedEmailTemplates[0];

    const [identityUpdatedEmailTemplates] = await queryInterface.sequelize.query(`
      SELECT notification_email_id FROM notification_email
      WHERE name = 'Identity Updated Notification'
      LIMIT 1;
    `);
    const identityUpdatedEmailTemplate = identityUpdatedEmailTemplates[0];

    await queryInterface.bulkInsert("notification_channel", [
      {
        notification_channel_id: uuidv4(),
        notification_id: notification.notification_id,
        channel: "email",
        notification_child_id: emailTemplate.notification_email_id,
        status: "Active",
        created_at: new Date(),
        updated_at: new Date(),
      },
      {
        notification_channel_id: uuidv4(),
        notification_id: notification.notification_id,
        channel: "text",
        notification_child_id: textTemplate.notification_text_id,
        status: "Active",
        created_at: new Date(),
        updated_at: new Date(),
      },
      {
        notification_channel_id: uuidv4(),
        notification_id: identityCreatedNotification.notification_id,
        channel: "email",
        notification_child_id: identityCreatedEmailTemplate.notification_email_id,
        status: "Active",
        created_at: new Date(),
        updated_at: new Date(),
      },
      {
        notification_channel_id: uuidv4(),
        notification_id: identityUpdatedNotification.notification_id,
        channel: "email",
        notification_child_id: identityUpdatedEmailTemplate.notification_email_id,
        status: "Active",
        created_at: new Date(),
        updated_at: new Date(),
      },
    ]);
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.bulkDelete("notification_channel", null, {});
  },
};
