'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.query(`
      CREATE VIEW "notification_config_view" AS
      SELECT 
        n.notification_id,
        n.name AS name,
        n.schema,
        n.schema_column,
        n.status AS notification_status,
        n.language AS notification_language,

        nc.channel
      FROM notification n
      LEFT JOIN notification_channel nc ON n.notification_id = nc.notification_id
      LEFT JOIN notification_email ne ON nc.notification_child_id = ne.notification_email_id
      LEFT JOIN notification_text nt ON nc.notification_child_id = nt.notification_text_id
    `);
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.query('DROP VIEW IF EXISTS "notification_config_view";');
  },
};
