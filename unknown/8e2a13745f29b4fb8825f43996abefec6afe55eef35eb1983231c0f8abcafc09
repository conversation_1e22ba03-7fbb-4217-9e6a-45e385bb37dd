module.exports = (sequelize, DataTypes) => {
    const NotificationConfigView = sequelize.define(
      "NotificationConfigView",
      {
        notification_id: {
          type: DataTypes.UUID,
          primaryKey: true,
        },
        name: {
          type: DataTypes.STRING,
        },
        schema: {
          type: DataTypes.STRING,
        },
        schema_column: {
          type: DataTypes.STRING,
        },
        notification_status: {
          type: DataTypes.STRING,
        },
        notification_language: {
          type: DataTypes.STRING,
        },
        notification_email_id: {
          type: DataTypes.UUID,
        },
        email_subject: {
          type: DataTypes.STRING,
        },
        email_template: {
          type: DataTypes.TEXT,
        },
        email_receiver_column: {
          type: DataTypes.STRING,
        },
        email_status: {
          type: DataTypes.STRING,
        },
        email_language: {
          type: DataTypes.STRING,
        },
        notification_text_id: {
          type: DataTypes.UUID,
        },
        text_template: {
          type: DataTypes.TEXT,
        },
        text_receiver_column: {
          type: DataTypes.STRING,
        },
        text_status: {
          type: DataTypes.STRING,
        },
        text_language: {
          type: DataTypes.STRING,
        },
        channel: {
          type: DataTypes.STRING,
        },
      },
      {
        tableName: "notification_config_view",
        timestamps: false,
      }
    );
  
    return NotificationConfigView;
  };
  