"use strict";
const { v4: uuidv4 } = require("uuid");

module.exports = {
  up: async (queryInterface) => {
    // First, get the agreement IDs from the previously seeded agreements
    const [agreements] = await queryInterface.sequelize.query(
      `SELECT nda_agreement_id, identity_id, status, signed_at FROM nda_agreement ORDER BY created_at;`
    );

    if (agreements.length === 0) {
      throw new Error("No NDA agreements found. Please run nda-agreement-seeder first.");
    }

    // Get identity IDs from the identity table for signers
    const [identities] = await queryInterface.sequelize.query(
      `SELECT identity_id FROM identity LIMIT 10;`
    );

    if (identities.length === 0) {
      throw new Error("No identities found. Please ensure identity data exists before running this seeder.");
    }

    // Create signatures using available agreements and identities
    const ndaSignatures = [];
    const signatureData = [
      {
        agreement_index: 0,
        signer_role: 0, // User (from master data)
        use_agreement_identity: true, // Use the identity from the agreement
        signature_method: 0, // E-Signature (from master data)
        signature: "/signatures/employee_001_signature.png",
        time_offset: 0, // Same time as agreement
      },
      {
        agreement_index: 1,
        signer_role: 0, // User (from master data)
        use_agreement_identity: true,
        signature_method: 2, // Digital Signature (from master data)
        signature: "/signatures/employee_002_digital.pdf",
        time_offset: 0,
      },
      {
        agreement_index: 1, // Same agreement, different signer
        signer_role: 1, // Admin (from master data)
        use_agreement_identity: false, // Use different identity
        signature_method: 0, // E-Signature (from master data)
        signature: "/signatures/admin_001_signature.png",
        time_offset: 3600000, // 1 hour later
      },
      {
        agreement_index: 3,
        signer_role: 3, // External Party (from master data)
        use_agreement_identity: true,
        signature_method: 1, // Manual Upload (from master data)
        signature: "/signatures/vendor_wet_signature.pdf",
        time_offset: 0,
      },
      {
        agreement_index: 4,
        signer_role: 2, // Legal Reviewer (from master data)
        use_agreement_identity: false,
        signature_method: 0, // E-Signature (from master data)
        signature: "/signatures/legal_001_signature.png",
        time_offset: -86400000, // 1 day before
      },
    ];

    // Create signatures using available agreements and identities
    for (let i = 0; i < signatureData.length; i++) {
      const data = signatureData[i];
      const agreementIndex = Math.min(data.agreement_index, agreements.length - 1);
      const agreement = agreements[agreementIndex];

      // Skip if agreement doesn't have signed_at (pending agreements)
      if (!agreement.signed_at) {
        continue;
      }

      // Determine signer identity
      let signerIdentityId;
      if (data.use_agreement_identity) {
        signerIdentityId = agreement.identity_id;
      } else {
        // Use a different identity for admin/legal reviewer
        const identityIndex = (i + 1) % identities.length;
        signerIdentityId = identities[identityIndex].identity_id;
      }

      // Calculate signature time
      const baseTime = new Date(agreement.signed_at);
      const signedAt = new Date(baseTime.getTime() + data.time_offset);

      ndaSignatures.push({
        nda_signature_id: uuidv4(),
        nda_agreement_id: agreement.nda_agreement_id,
        signer_role: data.signer_role,
        signer_id: signerIdentityId,
        signed_at: signedAt,
        signature_method: data.signature_method,
        signature: data.signature,
        created_at: new Date(),
        updated_at: new Date(),
      });
    }

    await queryInterface.bulkInsert("nda_signature", ndaSignatures, {});
  },

  down: async (queryInterface) => {
    await queryInterface.bulkDelete("nda_signature", null, {});
  },
};
