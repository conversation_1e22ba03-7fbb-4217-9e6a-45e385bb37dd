"use strict";

module.exports = {
  up: async (queryInterface, Sequelize) => {
    return queryInterface.sequelize.query(`
      CREATE VIEW view_outpatient_guest_current 
    AS SELECT pg.first_name AS first_name,
    pg.last_name AS last_name,
    a.appointment_date,
    a.appointment_id,
    a.provider_name,
    a.patient_id,
    ag.arrival_time AS appointment_arrival_time,
    ag.departure_time AS appointment_departure_time,
    a.location,
    ag.guest_pin,
    ag.screening,
    'g'as guest_type  
   FROM appointment a
     JOIN patient p ON a.patient_id = p.patient_id
     INNER JOIN appointment_guest ag ON a.appointment_id = ag.appointment_id
     INNER JOIN patient_guest pg ON ag.patient_guest_id = pg.patient_guest_id
  WHERE a.type = 1 AND date(a.appointment_date) = CURRENT_DATE
  union 
  SELECT p.first_name AS first_name,
    p.last_name AS last_name,
    a.appointment_date,
    a.appointment_id,
    a.provider_name,
    a.patient_id,
    a.arrival_time AS appointment_arrival_time,
    a.departure_time AS appointment_departure_time,
    a.location,
    null guest_pin ,
    null screening,
    'p'as guest_type
   FROM appointment a
     JOIN patient p ON a.patient_id = p.patient_id
  WHERE a.type = 1 AND date(a.appointment_date) = CURRENT_DATE;
       
    `);
  },

  down: async (queryInterface, Sequelize) => {
    return queryInterface.sequelize.query(`
      DROP VIEW IF EXISTS view_outpatient_guest_current;
    `);
  },
};
