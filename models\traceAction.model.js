module.exports = (sequelize, DataTypes) => {
  const TraceAction = sequelize.define(
    "TraceAction",
    {
      trace_action_id: {
        type: DataTypes.UUID,
        primaryKey: true,
        defaultValue: DataTypes.UUIDV4,
      },
      trace_id: {
        type: DataTypes.UUID,
        allowNull: false,
      },
      status: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      message: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
    },
    {
      tableName: "trace_action",
      timestamps: true,
      createdAt: "created_at",
      updatedAt: false,
      underscored: true,
    }
  );
  TraceAction.associate = (models) => {
    TraceAction.belongsTo(models.EventTrace, {
      foreignKey: "trace_id",
      as: "trace",
    });

    TraceAction.belongsTo(models.MasterData, {
      foreignKey: "status",
      targetKey: "key",
      as: "trace_action_status_name",
      constraints: false,
      scope: {
        group: "trace_action_status", // Ensure the `status` group is consistent with master data
      },
    });
  };
  return TraceAction;
};
