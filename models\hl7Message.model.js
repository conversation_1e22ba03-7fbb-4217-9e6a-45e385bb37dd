const history = require("../models/plugins/history.plugin");

module.exports = (sequelize, DataTypes) => {
  const Hl7Message = sequelize.define(
    "Hl7Message",
    {
      hl7_message_id: {
        type: DataTypes.UUID,
        primaryKey: true,
        defaultValue: DataTypes.UUIDV4,
      },
      hdr: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      mrn: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      message_type: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      message: {
        type: DataTypes.TEXT,
        allowNull: false,
      },
      processed_at: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      updated_by: {
        type: DataTypes.UUID,
        allowNull: true,
      },
    },
    {
      tableName: "hl7_message",
      timestamps: true,
      underscored: true,
      paranoid: true,
    }
  );

  history(Hl7Message, sequelize, DataTypes);

  return Hl7Message;
};
