module.exports = (sequelize, DataTypes) => {
  const OutpatientGuestFutureView = sequelize.define(
    "OutpatientGuestFutureView",
    {
      first_name: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      last_name: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      patient_id: {
        type: DataTypes.UUID,
        primaryKey: true,
      },
      provider_name: {
        type: DataTypes.STRING,
        allowNull:true,
      },
      appointment_id: {
            type: DataTypes.UUID,
            allowNull: true,
        },
      appointment_date: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      appointment_arrival_time: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      appointment_departure_time: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      location: {
        type: DataTypes.STRING,
        allowNull: true,
      },
    
      guest_pin: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      screening: {
        type: DataTypes.BOOLEAN,
        allowNull: true,
      },
      guest_type: {
        type: DataTypes.STRING,
        allowNull: true,
      },
    },
    {
      tableName: "view_outpatient_guest_future",
      timestamps: false,
      underscored: true,
    }
  );

  return OutpatientGuestFutureView;
};
