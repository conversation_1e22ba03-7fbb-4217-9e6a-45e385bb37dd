const history = require("../models/plugins/history.plugin");

module.exports = (sequelize, DataTypes) => {
  const HipaaCompliance = sequelize.define(
    "HipaaCompliance",
    {
      id: {
        type: DataTypes.UUID,
        primaryKey: true,
        defaultValue: DataTypes.UUIDV4,
      },
      identity_id: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: "identity",
          key: "identity_id",
        },
        onDelete: "CASCADE",
      },
      patient_id: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: "patient",
          key: "patient_id",
        },
        onDelete: "CASCADE",
      },
      endpoint: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      action_time: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
      },
    },
    {
      tableName: "hipaa_compliance",
      timestamps: true,
      underscored: true,
    }
  );

  HipaaCompliance.associate = (models) => {
    HipaaCompliance.belongsTo(models.Identity, {
      foreignKey: "identity_id",
      as: "identity",
      onDelete: "CASCADE",
    });
    HipaaCompliance.belongsTo(models.Patient, {
      foreignKey: "patient_id",
      as: "patient",
      onDelete: "CASCADE",
    });
  };

  history(HipaaCompliance, sequelize, DataTypes);

  return HipaaCompliance;
};
