[{"name": "Facility status is active rule", "conditions": {"all": [{"fact": "status", "operator": "equal", "value": 0}, {"fact": "application", "operator": "equal", "value": "api"}]}, "event": {"type": "facilityStatusActive", "params": {"message": "The facility status is active"}}}, {"name": "Facility old status is inactive rule", "conditions": {"all": [{"fact": "changed.status", "operator": "equal", "value": true}, {"fact": "application", "operator": "equal", "value": "api"}]}, "event": {"type": "facilityStatusChanged", "params": {"message": "The facility old status is inactive"}}}]