const morgan = require('morgan');
const axios = require('axios');
const config = require('./config');
const logger = require('./logger');
const tykConfig = require('./tyk');

morgan.token('message', (req, res) => res.locals.errorMessage || '');
morgan.token('identity', (req, res) => req.identity?.identity_id || 'anonymous');
morgan.token('auth-mode', (req, res) => config.auth.mode);

const getIpFormat = () => (config.env === 'production' ? ':remote-addr - ' : '');
const successResponseFormat = `${getIpFormat()}:method :url :status - :response-time ms - identity: :identity - auth: :auth-mode`;
const errorResponseFormat = `${getIpFormat()}:method :url :status - :response-time ms - identity: :identity - auth: :auth-mode - message: :message`;

/**
 * Send logs to Tyk Analytics if Tyk mode is enabled
 */
const sendToTykAnalytics = async (logData) => {
  if (config.auth.mode !== 'tyk' || !tykConfig.isEnabled) {
    return;
  }

  try {
    const analyticsData = {
      timestamp: new Date().toISOString(),
      method: logData.method,
      url: logData.url,
      status: logData.status,
      response_time: logData.responseTime,
      identity_id: logData.identity,
      ip_address: logData.ip,
      user_agent: logData.userAgent,
      auth_mode: logData.authMode
    };

    // Send to Tyk Analytics endpoint
    await axios.post(
      `${tykConfig.gatewayUrl}/tyk/analytics`,
      analyticsData,
      {
        headers: tykConfig.getGatewayHeaders(),
        timeout: 5000
      }
    );
  } catch (error) {
    // Don't fail the request if analytics logging fails
    logger.warn('Failed to send analytics to Tyk:', error.message);
  }
};

const successHandler = morgan(successResponseFormat, {
  skip: (req, res) => res.statusCode >= 400,
  stream: {
    write: (message) => {
      logger.info(message.trim());

      // Send to Tyk Analytics if enabled
      if (config.auth.mode === 'tyk') {
        const logData = {
          method: req.method,
          url: req.originalUrl,
          status: res.statusCode,
          responseTime: res.get('X-Response-Time'),
          identity: req.identity?.identity_id || 'anonymous',
          ip: req.ip,
          userAgent: req.get('User-Agent'),
          authMode: config.auth.mode
        };
        sendToTykAnalytics(logData).catch(() => {}); // Fire and forget
      }
    }
  },
});

const errorHandler = morgan(errorResponseFormat, {
  skip: (req, res) => res.statusCode < 400,
  stream: {
    write: (message) => {
      logger.error(message.trim());

      // Send to Tyk Analytics if enabled
      if (config.auth.mode === 'tyk') {
        const logData = {
          method: req.method,
          url: req.originalUrl,
          status: res.statusCode,
          responseTime: res.get('X-Response-Time'),
          identity: req.identity?.identity_id || 'anonymous',
          ip: req.ip,
          userAgent: req.get('User-Agent'),
          authMode: config.auth.mode,
          error: res.locals.errorMessage
        };
        sendToTykAnalytics(logData).catch(() => {}); // Fire and forget
      }
    }
  },
});

module.exports = {
  successHandler,
  errorHandler,
};
